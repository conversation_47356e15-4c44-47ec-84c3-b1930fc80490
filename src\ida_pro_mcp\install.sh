#!/bin/bash

echo "================================================================"
echo "IDA Pro MCP Plugin Installer (Linux/macOS)"
echo "Enhanced AI-Powered Reverse Engineering"
echo "================================================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.11 or higher"
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "ERROR: Python 3.11 or higher is required"
    echo "Current version: $python_version"
    exit 1
fi

echo "Running installer..."
python3 "$(dirname "$0")/installer.py"

if [ $? -ne 0 ]; then
    echo
    echo "Installation failed. Please check the error messages above."
    exit 1
fi

echo
echo "================================================================"
echo "Installation completed successfully!"
echo
echo "Next steps:"
echo "1. Restart IDA Pro"
echo "2. Restart your MCP clients (Cursor, Cline, etc.)"
echo "3. In IDA Pro: Edit -> Plugins -> MCP to start the server"
echo "4. Test with check_connection tool in your MCP client"
echo "================================================================"
