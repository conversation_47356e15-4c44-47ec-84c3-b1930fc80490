@echo off
echo ================================================================
echo IDA Pro MCP Plugin Installer (Windows)
echo Enhanced AI-Powered Reverse Engineering
echo ================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11 or higher from https://python.org
    pause
    exit /b 1
)

echo Running installer...
python "%~dp0installer.py"

if errorlevel 1 (
    echo.
    echo Installation failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ================================================================
echo Installation completed successfully!
echo.
echo Next steps:
echo 1. Restart IDA Pro
echo 2. Restart your MCP clients (Cursor, Cline, etc.)
echo 3. In IDA Pro: Edit -^> Plugins -^> MCP to start the server
echo 4. Test with check_connection tool in your MCP client
echo ================================================================
pause
